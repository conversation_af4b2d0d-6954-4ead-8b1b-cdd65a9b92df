const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const path = require('path');
require('dotenv').config();

let sequelize;
let socketService;

// 安全地导入数据库和服务
try {
  const dbConfig = require('./config/database');
  sequelize = dbConfig.sequelize;
  socketService = require('./services/socketService');
} catch (error) {
  console.error('❌ 数据库配置加载失败:', error.message);
  console.log('⚠️ 应用将在没有数据库的情况下运行');
}

// 安全地导入路由
let authRoutes, userRoutes, roleRoutes, equipmentRoutes, orderRoutes, engineerRoutes, adminRoutes, chatRoutes, uploadRoutes;

// 逐个尝试加载路由，避免一个失败影响全部
const loadRoute = (routePath, routeName) => {
  try {
    return require(routePath);
  } catch (error) {
    console.error(`❌ ${routeName}路由加载失败:`, error.message);
    return null;
  }
};

authRoutes = loadRoute('./routes/auth', '认证');
userRoutes = loadRoute('./routes/users', '用户');
roleRoutes = loadRoute('./routes/roles', '角色');
equipmentRoutes = loadRoute('./routes/equipment', '设备');
orderRoutes = loadRoute('./routes/orders', '工单');
engineerRoutes = loadRoute('./routes/engineers', '工程师');
adminRoutes = loadRoute('./routes/admin', '管理员');
chatRoutes = loadRoute('./routes/chat', '聊天');
uploadRoutes = loadRoute('./routes/upload', '上传');

const app = express();
const PORT = process.env.PORT || 80;

// 信任代理设置 - 云托管环境需要
app.set('trust proxy', true);

// 中间件配置
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://your-domain.com']
    : ['http://localhost:3000'],
  credentials: true
}));

// 请求限制 - 针对云托管环境优化
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 200, // 增加限制数量，适应小程序频繁请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  },
  // 标准化响应
  standardHeaders: true,
  legacyHeaders: false,
  // 跳过某些请求
  skip: (req) => {
    // 跳过健康检查请求
    return req.path === '/health' || req.path === '/api/health';
  }
});

// 只在非开发环境启用速率限制
if (process.env.NODE_ENV !== 'development') {
  app.use('/api/', limiter);
}

app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API路由 - 安全注册
if (authRoutes) app.use('/api/auth', authRoutes);
if (userRoutes) app.use('/api/users', userRoutes);
if (roleRoutes) app.use('/api/roles', roleRoutes);
if (equipmentRoutes) app.use('/api/equipment', equipmentRoutes);
if (orderRoutes) app.use('/api/orders', orderRoutes);
if (engineerRoutes) app.use('/api/engineers', engineerRoutes);
if (adminRoutes) app.use('/api/admin', adminRoutes);
if (chatRoutes) app.use('/api/chat', chatRoutes);
if (uploadRoutes) app.use('/api/upload', uploadRoutes);

// 如果路由加载失败，提供基本的测试路由和必要的API
if (!authRoutes || !orderRoutes || !adminRoutes) {
  console.log('⚠️ 部分路由加载失败，启用简化模式');

  // 基本测试路由
  app.get('/api/test', (req, res) => {
    res.json({
      success: true,
      message: '基本API测试成功',
      timestamp: new Date().toISOString(),
      note: '完整路由未加载，请检查数据库连接'
    });
  });

  // 简化的登录接口
  if (!authRoutes) {
    app.post('/api/auth/wechat-login', (req, res) => {
      res.json({
        success: true,
        message: '登录成功（简化模式）',
        data: {
          user: {
            id: 1,
            username: '测试用户',
            role: 'user'
          },
          token: 'wx_cloud_test_token',
          refreshToken: 'wx_cloud_test_refresh'
        }
      });
    });
  }

  // 简化的工单接口 - 无需认证版本
  app.get('/api/orders/my', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  app.get('/api/orders', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  // 简化的设备接口 - 无需认证版本
  app.get('/api/equipment', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  app.get('/api/equipment/my', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  // 简化的聊天接口 - 无需认证版本
  app.get('/api/chat/messages', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  app.get('/api/chat/list', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    });
  });

  app.post('/api/chat/send', (req, res) => {
    res.json({
      success: true,
      message: '消息发送成功（简化模式）',
      data: {
        id: Date.now(),
        content: req.body.content || '测试消息',
        created_at: new Date()
      }
    });
  });

  // 简化的公告接口 - 不需要管理员权限
  app.get('/api/announcements', (req, res) => {
    res.json({
      success: true,
      data: {
        list: [
          {
            id: 1,
            title: '系统维护通知',
            content: '系统正在维护中，部分功能可能受限',
            status: 'active',
            created_at: new Date(),
            updated_at: new Date()
          }
        ],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1
      }
    });
  });

  // 管理员公告接口的备用版本
  if (!adminRoutes) {
    app.get('/api/admin/announcements', (req, res) => {
      res.json({
        success: true,
        data: {
          list: [
            {
              id: 1,
              title: '系统维护通知',
              content: '系统正在维护中，部分功能可能受限',
              status: 'active',
              created_at: new Date(),
              updated_at: new Date()
            }
          ],
          total: 1,
          page: 1,
          limit: 10,
          totalPages: 1
        }
      });
    });
  }

  // 简化的用户接口
  if (!userRoutes) {
    app.get('/api/users/profile', (req, res) => {
      res.json({
        success: true,
        data: {
          id: 1,
          username: '测试用户',
          real_name: '测试用户',
          role: 'user',
          is_verified: false,
          is_active: true
        }
      });
    });

    app.get('/api/users/notifications/unread-count', (req, res) => {
      res.json({
        success: true,
        data: {
          count: 0
        }
      });
    });

    // 用户统计接口
    app.get('/api/users/stats', (req, res) => {
      res.json({
        success: true,
        data: {
          myOrders: 0,
          myEquipment: 0,
          unreadMessages: 0,
          assignedOrders: 0,
          completedOrders: 0
        }
      });
    });
  }

  // 简化的角色接口
  if (!roleRoutes) {
    app.get('/api/roles/my-roles', (req, res) => {
      res.json({
        success: true,
        data: {
          roles: ['user'],
          currentRole: 'user',
          isMultiRole: false,
          needRoleSelection: false
        }
      });
    });
  }

  // 补充工单接口
  if (!orderRoutes) {
    app.get('/api/orders/assigned', (req, res) => {
      res.json({
        success: true,
        data: {
          list: [],
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0
        }
      });
    });
  }
}

// 健康检查
app.get('/health', (req, res) => {
  const cloudHeaders = {
    openid: req.headers['x-wx-openid'],
    unionid: req.headers['x-wx-unionid'],
    appid: req.headers['x-wx-appid'],
    source: req.headers['x-wx-source'],
    sessionKey: req.headers['x-wx-session-key']
  };

  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    cloudUserInfo: {
      hasOpenid: !!cloudHeaders.openid,
      hasUnionid: !!cloudHeaders.unionid,
      appid: cloudHeaders.appid,
      source: cloudHeaders.source,
      hasSessionKey: !!cloudHeaders.sessionKey,
      openidSuffix: cloudHeaders.openid ? '***' + cloudHeaders.openid.slice(-4) : null
    }
  });
});

// API健康检查（带详细信息）
app.get('/api/health', (req, res) => {
  const cloudHeaders = {
    openid: req.headers['x-wx-openid'],
    unionid: req.headers['x-wx-unionid'],
    appid: req.headers['x-wx-appid'],
    source: req.headers['x-wx-source'],
    sessionKey: req.headers['x-wx-session-key'],
    version: req.headers['x-wx-version'],
    scene: req.headers['x-wx-scene']
  };

  res.json({
    success: true,
    message: '服务正常',
    data: {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      cloudUserInfo: {
        hasOpenid: !!cloudHeaders.openid,
        hasUnionid: !!cloudHeaders.unionid,
        appid: cloudHeaders.appid,
        source: cloudHeaders.source,
        version: cloudHeaders.version,
        scene: cloudHeaders.scene,
        hasSessionKey: !!cloudHeaders.sessionKey,
        openidSuffix: cloudHeaders.openid ? '***' + cloudHeaders.openid.slice(-4) : null
      },
      allHeaders: Object.keys(req.headers).filter(key => key.startsWith('x-wx-'))
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({ 
    success: false, 
    message: '接口不存在' 
  });
});

// 全局错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: '数据验证失败',
      errors: err.details
    });
  }
  
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      success: false,
      message: '未授权访问'
    });
  }
  
  res.status(500).json({
    success: false,
    message: process.env.NODE_ENV === 'production' 
      ? '服务器内部错误' 
      : err.message
  });
});

// 启动服务器
const server = app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 服务器运行在 0.0.0.0:${PORT}`);

  try {
    // 测试数据库连接
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 同步数据库模型
    // 在云托管环境中也需要同步，因为可能是首次部署
    try {
      // 先尝试强制同步，确保表结构正确
      await sequelize.sync({ force: false, alter: true });
      console.log('✅ 数据库模型同步完成');
    } catch (syncError) {
      console.error('❌ 数据库模型同步失败:', syncError.message);

      // 如果同步失败，尝试创建基本表结构
      try {
        console.log('🔧 尝试创建基本表结构...');

        // 创建user_roles表
        await sequelize.query(`
          CREATE TABLE IF NOT EXISTS \`user_roles\` (
            \`id\` int(11) NOT NULL AUTO_INCREMENT,
            \`user_id\` int(11) NOT NULL,
            \`role\` varchar(50) NOT NULL,
            \`role_name\` varchar(50) NOT NULL,
            \`department\` varchar(100) DEFAULT NULL,
            \`research_group_id\` int(11) DEFAULT NULL,
            \`permissions\` text DEFAULT NULL,
            \`is_active\` tinyint(1) DEFAULT 1,
            \`is_default\` tinyint(1) DEFAULT 0,
            \`granted_by\` int(11) DEFAULT NULL,
            \`granted_at\` datetime DEFAULT NULL,
            \`expires_at\` datetime DEFAULT NULL,
            \`created_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            \`updated_at\` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (\`id\`),
            UNIQUE KEY \`user_role_unique\` (\`user_id\`, \`role\`)
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        `);

        console.log('✅ 基本表结构创建完成');

        // 再次尝试同步
        await sequelize.sync({ alter: true });
        console.log('✅ 数据库模型重新同步完成');

      } catch (createError) {
        console.error('❌ 创建表结构也失败:', createError.message);
        console.log('⚠️ 请检查数据库权限或手动执行初始化脚本');
      }
    }
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.log('⚠️ 应用将在没有数据库连接的情况下继续运行');
    console.log('📝 请在微信云托管控制台配置数据库环境变量');
  }
});

// 初始化Socket.IO
if (socketService) {
  try {
    socketService.init(server);
    console.log('✅ Socket.IO 初始化成功');
  } catch (error) {
    console.error('❌ Socket.IO 初始化失败:', error.message);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
